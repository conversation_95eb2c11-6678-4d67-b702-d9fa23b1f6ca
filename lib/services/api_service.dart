import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/agency.dart';
import '../models/officer.dart';
import '../models/rating.dart';

class ApiService {
  static const String baseUrl = 'https://ketnoi.dichvucongcamau.gov.vn';
  static const String ssoUrl = 'https://sso.dichvucongcamau.gov.vn';
  static const String clientCredentials = 'Basic d2ViLXFuYS1wdWJsaWM6NGQzODM4NmQtZDRiZC00YzIxLTg0NzEtMjk1OGY4YjI5NmQ0';

  static String? _accessToken;
  static DateTime? _tokenExpiry;

  // Lấy access token từ SSO
  static Future<String> _getAccessToken() async {
    // Kiểm tra token còn hiệu lực không
    if (_accessToken != null &&
        _tokenExpiry != null &&
        DateTime.now().isBefore(_tokenExpiry!)) {
      return _accessToken!;
    }

    try {
      final url = Uri.parse('$ssoUrl/auth/realms/digo/protocol/openid-connect/token');

      final response = await http.post(
        url,
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5',
          'Authorization': clientCredentials,
          'Connection': 'keep-alive',
          'Content-type': 'application/x-www-form-urlencoded',
          'Referer': 'https://qnacamau.dichvucongcamau.gov.vn/',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-site',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
          'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"Windows"',
        },
        body: 'grant_type=client_credentials&scope=openid',
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        _accessToken = jsonData['access_token'];

        // Tính thời gian hết hạn (trừ đi 5 phút để đảm bảo)
        final expiresIn = jsonData['expires_in'] ?? 3600;
        _tokenExpiry = DateTime.now().add(Duration(seconds: expiresIn - 300));

        return _accessToken!;
      } else {
        throw Exception('Failed to get access token: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error getting access token: $e');
    }
  }

  // Lấy headers với authentication
  static Future<Map<String, String>> _getAuthHeaders() async {
    final token = await _getAccessToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Lấy danh sách đơn vị
  static Future<AgencyResponse> getAgencies({
    int page = 1,
    int size = 10,
    String? parentId = '000000000191c4e1bd300012',
  }) async {
    try {
      // Xây dựng URL với hoặc không có parent-id
      String urlString = '$baseUrl/ba/agency/name+logo-id?page=$page&size=$size';
      if (parentId != null) {
        urlString += '&parent-id=$parentId';
      }
      final url = Uri.parse(urlString);

      print('🔗 Calling API: $url');

      // Thử gọi API với authentication trước
      try {
        final headers = await _getAuthHeaders();
        print('🔑 Using authenticated headers');
        final response = await http.get(url, headers: headers);

        print('📡 Response status: ${response.statusCode}');
        print('📄 Response body: ${response.body.substring(0, response.body.length > 200 ? 200 : response.body.length)}...');

        if (response.statusCode == 200) {
          final jsonData = json.decode(utf8.decode(response.bodyBytes));
          return AgencyResponse.fromJson(jsonData);
        } else {
          print('❌ Auth API failed with status: ${response.statusCode}');
          throw Exception('Auth API failed: ${response.statusCode}');
        }
      } catch (authError) {
        print('⚠️ Authentication failed: $authError');
        print('🔄 Trying without authentication...');

        // Fallback: thử gọi API mà không cần authentication
        final basicHeaders = {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        };

        final response = await http.get(url, headers: basicHeaders);
        print('📡 Fallback response status: ${response.statusCode}');

        if (response.statusCode == 200) {
          final jsonData = json.decode(utf8.decode(response.bodyBytes));
          return AgencyResponse.fromJson(jsonData);
        } else {
          throw Exception('Both auth and non-auth API failed: ${response.statusCode}');
        }
      }
    } catch (e) {
      print('💥 Final error: $e');
      throw Exception('Error fetching agencies: $e');
    }
  }

  // Lấy nhiều agency cùng lúc (load sẵn 500 đơn vị)
  static Future<List<Agency>> getAllAgencies({
    String? parentId, // Bỏ default để lấy tất cả
    int maxAgencies = 500,
  }) async {
    List<Agency> tempAgencies = []; // Biến tạm lưu list
    int currentPage = 0;
    int batchSize = 10; // Bắt đầu với 10
    bool hasMore = true;

    try {
      while (hasMore && currentPage <= 9) { // Chỉ load từ page 0-9
        print('🔄 Loading agencies page $currentPage, size $batchSize (current total: ${tempAgencies.length})...');

        final response = await getAgencies(
          page: currentPage,
          size: batchSize,
          parentId: parentId,
        );

        print('📦 Page $currentPage returned ${response.content.length} agencies, last: ${response.last}');

        // Thêm vào biến tạm
        tempAgencies.addAll(response.content);

        // Kiểm tra xem còn trang nào không và chưa đến page 9
        hasMore = response.content.length == batchSize && !response.last && currentPage < 9;
        print('🔍 hasMore: $hasMore (content.length: ${response.content.length}, batchSize: $batchSize, last: ${response.last}, currentPage: $currentPage)');

        // Tăng page và size cho lần gọi tiếp theo: 0,10 -> 1,20 -> 2,30 -> ...
        currentPage++;
        batchSize += 10; // Tăng size: 10 -> 20 -> 30 -> ...

        // Giới hạn batchSize tối đa
        if (batchSize > 100) {
          batchSize = 100;
        }

        // Dừng nếu đã đến page 9
        if (currentPage > 9) {
          print('🛑 Reached page limit (0-9): stopping at page ${currentPage - 1}');
          break;
        }

        // Delay nhỏ để tránh spam API
        await Future.delayed(const Duration(milliseconds: 300));
      }

      print('✅ Loaded ${tempAgencies.length} agencies total');
      return tempAgencies;
    } catch (e) {
      print('💥 Error loading all agencies: $e');
      // Trả về danh sách đã load được (nếu có)
      return tempAgencies;
    }
  }

  // Lấy danh sách cán bộ theo đơn vị
  static Future<OfficerResponse> getOfficers({
    required String agencyId,
    int page = 0,
    int size = 10,
    String keyword = '',
    String role = 'IGATE_XU_LY_HO_SO',
    bool isEnableAgency = false,
  }) async {
    try {
      final url = Uri.parse(
        '$baseUrl/hu/user/detail+experience?spec=page&sort=fullname,asc&page=$page&size=$size&keyword=$keyword&agency-id=$agencyId&role=$role&isEnableAgency=$isEnableAgency',
      );

      final headers = await _getAuthHeaders();
      final response = await http.get(url, headers: headers);

      if (response.statusCode == 200) {
        final jsonData = json.decode(utf8.decode(response.bodyBytes));
        return OfficerResponse.fromJson(jsonData);
      } else {
        throw Exception('Failed to load officers: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching officers: $e');
    }
  }

  // Lấy danh sách đánh giá
  static Future<RatingResponse> getRatingOptions({
    required String deploymentId,
    required String agencyId,
    int page = 0,
    int size = 10,
    int status = 1,
  }) async {
    try {
      final url = Uri.parse(
        '$baseUrl/su/rating-officer/?spec=page&page=$page&size=$size&deployment-id=$deploymentId&agency-id=$agencyId&status=$status',
      );

      final headers = await _getAuthHeaders();
      final response = await http.get(url, headers: headers);

      if (response.statusCode == 200) {
        final jsonData = json.decode(utf8.decode(response.bodyBytes));
        return RatingResponse.fromJson(jsonData);
      } else {
        throw Exception('Failed to load rating options: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching rating options: $e');
    }
  }

  // Gửi đánh giá
  static Future<bool> submitRating(RatingSubmission rating) async {
    try {
      final url = Uri.parse('$baseUrl/su/rating-officer/submit');

      final headers = await _getAuthHeaders();
      final response = await http.post(
        url,
        headers: headers,
        body: json.encode(rating.toJson()),
      );

      return response.statusCode == 200 || response.statusCode == 201;
    } catch (e) {
      throw Exception('Error submitting rating: $e');
    }
  }

  // Method để clear token khi cần (ví dụ khi logout)
  static void clearToken() {
    _accessToken = null;
    _tokenExpiry = null;
  }

  // Test method để kiểm tra API
  static Future<void> testApi() async {
    try {
      print('🧪 Testing API connection...');
      final url = Uri.parse('$baseUrl/ba/agency/name+logo-id?page=1&size=5&parent-id=000000000191c4e1bd300012');

      final response = await http.get(url, headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      });

      print('🧪 Test response status: ${response.statusCode}');
      print('🧪 Test response body: ${response.body.substring(0, response.body.length > 300 ? 300 : response.body.length)}...');
    } catch (e) {
      print('🧪 Test error: $e');
    }
  }
}
